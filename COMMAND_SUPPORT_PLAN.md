# Command Support Implementation Plan

This document outlines the plan to add command support to the HotPreview framework, following the existing pattern used for Previews.

## Overview

Commands will be static methods marked with a `[PreviewCommand]` attribute that can be executed from the DevTools UI. They will be discovered using both reflection and Roslyn-based analysis, similar to how Previews work.

## Implementation Tasks

### 1. Create PreviewCommand Attribute (Core Library)
**File:** `src/HotPreview/PreviewCommandAttribute.cs`
- Create `PreviewCommandAttribute` class similar to `PreviewAttribute`
- Support optional `DisplayName` parameter for UI display
- Use `AttributeTargets.Method` only (commands are methods only)
- Include `TypeFullName` static property for tooling

### 2. Add Command Discovery to UIComponentsManagerBase (Reflection Version)
**Files to modify:**
- `src/HotPreview.SharedModel/UIComponentsManagerBase.cs`
- `src/HotPreview.SharedModel/App/UIComponentsManagerReflection.cs`

**Changes needed:**
- Add `Commands` property to store discovered commands
- Add `GetCommand(string name)` method
- Update UI to include "Commands" section (no categories, just a flat list)

### 3. Create Command Reflection Class
**New file to create:**
- `src/HotPreview.SharedModel/App/CommandReflection.cs` - Command class with static method support

**Features:**
- Parameter validation (reject methods with parameters)
- Command name generation (similar to preview naming)
- Static method invocation support
- Inherits from appropriate base class

### 4. Add Command Discovery to Roslyn-based Tooling
**Files to modify:**
- `src/tooling/HotPreview.Tooling/UIComponentsManagerTooling.cs`

**New file:**
- `src/tooling/HotPreview.Tooling/CommandTooling.cs` - Roslyn-based command discovery

### 5. Add InvokeCommandAsync to PreviewAppService
**File:** `src/HotPreview.SharedModel/App/PreviewAppService.cs`

**New methods:**
- `Task InvokeCommandAsync(string commandName)` - Execute command by name
- `Task<string[]> GetCommandsAsync()` - Get list of available commands
- Helper methods for command lookup and validation

### 6. Update DevToolsApp UI for Commands
**Files to modify:**
- `src/tooling/HotPreview.DevToolsApp/ViewModels/MainPageViewModel.cs`
- `src/tooling/HotPreview.DevToolsApp/ViewModels/NavTree/NavTreeItemViewModel.cs`

**New file:**
- `src/tooling/HotPreview.DevToolsApp/ViewModels/NavTree/CommandViewModel.cs`

**Changes:**
- Add Commands section to tree view (alongside Controls/Pages)
- Create `CommandViewModel` for tree display
- Update `UpdateNavTreeItems()` to include commands as a flat list
- Add status bar message for single-click: "Double click to execute command"

### 7. Implement Double-Click Command Execution
**Files to modify:**
- `src/tooling/HotPreview.DevToolsApp/ViewModels/NavTree/CommandViewModel.cs`
- Update XAML for double-click handling if needed

**Features:**
- Override `OnItemInvoked()` for double-click detection
- Call `PreviewAppService.InvokeCommandAsync()`
- Update status bar with execution results
- Handle errors gracefully

### 8. Parameter Validation
**Implementation locations:**
- `CommandReflection.cs` - Runtime validation
- Command discovery code - Build-time validation
- Error messages for methods with parameters

## Technical Details

### Command Naming Convention
Commands will follow the same naming pattern as previews:
- Full name: `Namespace.ClassName.MethodName`
- Display name: From attribute or method name

### UI Integration
Commands will appear in the DevTools tree view as a simple flat list:
```
COMMANDS
├── Command1
├── Command2
└── Command3
```

### Error Handling
- Methods with parameters will be rejected with clear error messages
- Command execution failures will be reported in status bar
- Invalid commands will be filtered out during discovery

### Protocol Support
Commands will integrate with the existing JSON-RPC protocol used for tool-to-app communication.

## File Structure Summary

### New Files:
1. `src/HotPreview/PreviewCommandAttribute.cs`
2. `src/HotPreview.SharedModel/App/CommandReflection.cs`
3. `src/tooling/HotPreview.Tooling/CommandTooling.cs`
4. `src/tooling/HotPreview.DevToolsApp/ViewModels/NavTree/CommandViewModel.cs`

### Modified Files:
1. `src/HotPreview.SharedModel/UIComponentsManagerBase.cs`
2. `src/HotPreview.SharedModel/App/UIComponentsManagerReflection.cs`
3. `src/tooling/HotPreview.Tooling/UIComponentsManagerTooling.cs`
4. `src/HotPreview.SharedModel/App/PreviewAppService.cs`
5. `src/tooling/HotPreview.DevToolsApp/ViewModels/MainPageViewModel.cs`

## Testing Strategy
- Add unit tests for command discovery
- Test parameter validation
- Integration tests for command execution
- UI tests for tree view display and interaction